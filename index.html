<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hand-Forged Copper Sinks & Bathtubs | Tembaga Kayu Artisan Fixtures</title>
    <meta name="description" content="Hand-forged copper sinks and bathtubs with natural antimicrobial properties and living patina. Artisanal craftsmanship meets contemporary design.">
    <meta name="keywords" content="hand-forged copper sinks, copper bathtubs, antimicrobial copper, artisan copper fixtures, custom copper sinks">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tembagakayu.com/">
    <meta property="og:title" content="Hand-Forged Copper Sinks & Bathtubs | Tembaga Kayu">
    <meta property="og:description" content="Each sink and bathtub is shaped layer by layer in dialogue between artisan and element, offering natural antimicrobial protection and a living patina that deepens with time.">
    <meta property="og:image" content="https://tembagakayu.com/images/TK-website-1.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://tembagakayu.com/">
    <meta property="twitter:title" content="Hand-Forged Copper Sinks & Bathtubs | Tembaga Kayu">
    <meta property="twitter:description" content="Each sink and bathtub is shaped layer by layer in dialogue between artisan and element, offering natural antimicrobial protection and a living patina that deepens with time.">
    <meta property="twitter:image" content="https://tembagakayu.com/images/TK-website-1.jpg">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="wp-content/themes/tembagakayu/style.css" as="style">
    <link rel="preload" href="images/TK-website-1.jpg" as="image">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="wp-content/themes/tembagakayu/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Source+Sans+Pro:wght@300;400;600&display=swap" rel="stylesheet">
    
    <!-- Critical CSS -->
    <style>
    body{font-family:Georgia,serif;margin:0;padding:0;background:#faf8f5}
    .hero{height:100vh;min-height:600px;background:linear-gradient(rgba(44,24,16,.6),rgba(44,24,16,.6)),url("images/TK-website-1.jpg");background-size:cover;background-position:center;display:flex;align-items:center;justify-content:center;text-align:center;color:white}
    .hero h1{font-size:4rem;margin-bottom:1.5rem;text-shadow:2px 2px 4px rgba(0,0,0,.5)}
    .btn{display:inline-block;padding:15px 30px;margin:10px;text-decoration:none;border-radius:5px;font-weight:600;font-size:1.1rem;transition:all .3s ease}
    .btn-primary{background-color:#b87333;color:white}
    .btn-secondary{background:transparent;color:white;border:2px solid white}
    </style>
    
    <!-- Google Analytics 4 -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    
    <!-- Facebook Pixel -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', 'YOUR_PIXEL_ID');
        fbq('track', 'PageView');
    </script>
    
    <!-- Hotjar Tracking Code -->
    <script>
        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:YOUR_HOTJAR_ID,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>
</head>
<body>

<!-- Sticky CTA Header -->
<div id="sticky-cta" class="sticky-cta" style="display: none;">
    <div class="container">
        <span>Ready to transform your space with hand-forged copper?</span>
        <a href="#contact" class="btn btn-primary btn-small" onclick="gtag('event', 'click', {'event_category': 'CTA', 'event_label': 'Sticky Header'});">Begin the Dialogue</a>
    </div>
</div>

<!-- Hero Section -->
<section class="hero" id="hero">
    <div class="hero-content">
        <h1>Hand-Forged Copper Artistry<br>Where Time-Honored Techniques Meet Contemporary Design</h1>
        <p class="subheadline">Each sink and bathtub is shaped layer by layer in dialogue between artisan and element, offering natural antimicrobial protection and a living patina that deepens with time</p>
        <div class="hero-buttons">
            <a href="#contact" class="btn btn-primary" onclick="gtag('event', 'click', {'event_category': 'CTA', 'event_label': 'Hero Primary'});">Begin the Dialogue</a>
            <a href="#showcase" class="btn btn-secondary" onclick="gtag('event', 'click', {'event_category': 'CTA', 'event_label': 'Hero Secondary'});">Explore Our Craft</a>
        </div>
    </div>
</section>

<!-- Product Showcase Carousel -->
<section class="product-showcase" id="showcase">
    <div class="container">
        <div class="section-title">
            <h2>Artisan-Crafted Copper Fixtures</h2>
            <p class="section-subtitle">Each piece tells a story of traditional craftsmanship and modern functionality</p>
        </div>
        
        <div class="carousel-container">
            <!-- Slide 1 - Copper Sink -->
            <div class="carousel-slide active">
                <img src="images/TK-WEBSITE-PICTURE-1.png" alt="Hand-forged copper sink with natural patina finish in artisan workshop" onclick="openLightbox(this.src)">
                <div class="carousel-caption">
                    <h3>Artisan Copper Sink</h3>
                    <p>Dimensions: 24" x 18" x 8" | Finish: Natural Patina | Heat Retention: Superior</p>
                    <a href="#contact" class="btn btn-primary" onclick="gtag('event', 'click', {'event_category': 'Product', 'event_label': 'Sink Quote Request'});">Request Quote</a>
                </div>
            </div>
            
            <!-- Slide 2 - Copper Bathtub -->
            <div class="carousel-slide">
                <img src="images/TK-WEBSITE-PICTURE-2.png" alt="Luxury copper bathtub with antimicrobial properties and living patina" onclick="openLightbox(this.src)">
                <div class="carousel-caption">
                    <h3>Copper Soaking Tub</h3>
                    <p>Dimensions: 66" x 30" x 24" | Finish: Hammered Copper | Antimicrobial: Natural</p>
                    <a href="#contact" class="btn btn-primary" onclick="gtag('event', 'click', {'event_category': 'Product', 'event_label': 'Tub Quote Request'});">Request Quote</a>
                </div>
            </div>
            
            <!-- Slide 3 - Vessel Sink -->
            <div class="carousel-slide">
                <img src="images/TK-WEBSITE-PICTURE-3.png" alt="Copper vessel sink showcasing artisan hammering techniques" onclick="openLightbox(this.src)">
                <div class="carousel-caption">
                    <h3>Copper Vessel Sink</h3>
                    <p>Dimensions: 16" x 16" x 6" | Finish: Smooth Copper | Style: Contemporary</p>
                    <a href="#contact" class="btn btn-primary" onclick="gtag('event', 'click', {'event_category': 'Product', 'event_label': 'Vessel Quote Request'});">Request Quote</a>
                </div>
            </div>
            
            <!-- Slide 4 - Farmhouse Sink -->
            <div class="carousel-slide">
                <img src="images/TK-website-3.jpg" alt="Copper farmhouse sink with apron front design" onclick="openLightbox(this.src)">
                <div class="carousel-caption">
                    <h3>Farmhouse Copper Sink</h3>
                    <p>Dimensions: 30" x 20" x 10" | Finish: Aged Patina | Style: Traditional</p>
                    <a href="#contact" class="btn btn-primary" onclick="gtag('event', 'click', {'event_category': 'Product', 'event_label': 'Farmhouse Quote Request'});">Request Quote</a>
                </div>
            </div>
            
            <!-- Slide 5 - Double Basin -->
            <div class="carousel-slide">
                <img src="images/TK-website-7.jpg" alt="Double basin copper sink with divider for kitchen functionality" onclick="openLightbox(this.src)">
                <div class="carousel-caption">
                    <h3>Double Basin Copper Sink</h3>
                    <p>Dimensions: 32" x 18" x 9" | Finish: Brushed Copper | Configuration: 60/40 Split</p>
                    <a href="#contact" class="btn btn-primary" onclick="gtag('event', 'click', {'event_category': 'Product', 'event_label': 'Double Basin Quote Request'});">Request Quote</a>
                </div>
            </div>
            
            <!-- Slide 6 - Freestanding Tub -->
            <div class="carousel-slide">
                <img src="images/TK-website-9.jpg" alt="Freestanding copper bathtub with thermal conductivity benefits" onclick="openLightbox(this.src)">
                <div class="carousel-caption">
                    <h3>Freestanding Copper Tub</h3>
                    <p>Dimensions: 72" x 36" x 28" | Finish: Natural Copper | Heat Retention: 4x Steel</p>
                    <a href="#contact" class="btn btn-primary" onclick="gtag('event', 'click', {'event_category': 'Product', 'event_label': 'Freestanding Quote Request'});">Request Quote</a>
                </div>
            </div>
            
            <!-- Navigation -->
            <button class="carousel-nav carousel-prev" onclick="changeSlide(-1)">‹</button>
            <button class="carousel-nav carousel-next" onclick="changeSlide(1)">›</button>
        </div>
        
        <!-- Carousel Dots -->
        <div class="carousel-dots">
            <span class="carousel-dot active" onclick="currentSlide(1)"></span>
            <span class="carousel-dot" onclick="currentSlide(2)"></span>
            <span class="carousel-dot" onclick="currentSlide(3)"></span>
            <span class="carousel-dot" onclick="currentSlide(4)"></span>
            <span class="carousel-dot" onclick="currentSlide(5)"></span>
            <span class="carousel-dot" onclick="currentSlide(6)"></span>
        </div>
    </div>
</section>

<!-- Continue with rest of sections... -->
<script src="wp-content/themes/tembagakayu/js/main.js"></script>
</body>
</html>
