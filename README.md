# Tembaga Kayu - Hand-Forged Copper Fixtures Landing Page

A high-converting WordPress landing page for Tembaga Kayu's artisan copper sinks and bathtubs, featuring antimicrobial properties, living patina, and superior heat retention.

## Features

### 🎨 Design & User Experience
- **Hero Section**: Full-width background with compelling headlines and dual CTAs
- **Product Showcase**: 6-image carousel with lightbox zoom and quote request links
- **USP Grid**: Three-column layout highlighting copper's unique benefits
- **Custom Order Process**: 4-step infographic with lead magnet
- **Contact Form**: Comprehensive consultation request form
- **Responsive Design**: Optimized for all devices (375px+)

### 🚀 Performance Optimizations
- **Critical CSS**: Inline above-the-fold styles for faster LCP
- **Lazy Loading**: Images load as they enter viewport
- **WebP Support**: Modern image format with fallbacks
- **Gzip Compression**: Reduced file sizes
- **Browser Caching**: Optimized cache headers
- **Preloading**: Critical resources loaded first

### 📈 SEO & Analytics
- **Schema Markup**: Product schema for rich snippets
- **Meta Tags**: Optimized title, description, and keywords
- **Open Graph**: Social media sharing optimization
- **Google Analytics 4**: Event tracking for conversions
- **Facebook Pixel**: Retargeting and conversion tracking
- **Hotjar Integration**: User behavior analytics

### 🔧 Technical Features
- **Custom WordPress Theme**: Lightweight and fast
- **AJAX Forms**: Smooth form submissions without page reload
- **Touch Support**: Swipe gestures for carousel on mobile
- **Accessibility**: Proper alt text and semantic HTML
- **Security Headers**: XSS protection and content security

## Installation

### Prerequisites
- WordPress 5.0+
- PHP 7.4+
- MySQL 5.6+

### Setup Instructions

1. **Upload WordPress Files**
   ```bash
   # Files are already in place
   # Ensure wp-config.php has correct database credentials
   ```

2. **Database Configuration**
   - Create MySQL database: `tembagakayu_db`
   - Update wp-config.php with your database credentials
   - Run WordPress installation

3. **Theme Activation**
   - Go to Appearance > Themes in WordPress admin
   - Activate "Tembaga Kayu" theme

4. **Upload Images**
   - Images are already in `wp-content/uploads/2025/01/`
   - Ensure proper file permissions (644 for files, 755 for directories)

5. **Configure Analytics**
   - Replace `GA_MEASUREMENT_ID` with your Google Analytics ID
   - Replace `YOUR_PIXEL_ID` with your Facebook Pixel ID
   - Replace `YOUR_HOTJAR_ID` with your Hotjar site ID

## Content Management

### Adding New Products
1. Go to WordPress admin > Copper Products > Add New
2. Add product title, description, and featured image
3. Fill in product details (dimensions, finish, price range, features)
4. Publish the product

### Updating Carousel Images
1. Upload new images to Media Library
2. Edit the carousel section in `index.php`
3. Update image paths and alt text
4. Ensure images are optimized (WebP format recommended)

### Form Submissions
- Consultation requests are emailed to admin
- Guide downloads trigger automated email responses
- All submissions are tracked in Google Analytics

## Performance Monitoring

### Core Web Vitals Targets
- **LCP (Largest Contentful Paint)**: ≤ 2.5s
- **FID (First Input Delay)**: ≤ 100ms
- **CLS (Cumulative Layout Shift)**: ≤ 0.1

### Optimization Tools
- Use GTmetrix or PageSpeed Insights for performance testing
- Monitor Core Web Vitals in Google Search Console
- Check mobile usability regularly

## SEO Configuration

### Focus Keywords
- Primary: "hand-forged copper sinks"
- Secondary: "copper bathtubs", "antimicrobial copper fixtures"
- Long-tail: "custom copper sink artisan crafted"

### Content Guidelines
- Keep sentences under 20 words
- Use evocative verbs (hammer, reveal, patina)
- Include keywords in H1, meta title, and first paragraph
- Write descriptive alt text for all images

## Conversion Optimization

### CTA Placement
- Primary CTA after each major section
- Sticky header CTA on scroll
- Multiple quote request opportunities

### A/B Testing Opportunities
- Hero headline variations
- CTA button colors and text
- Form field requirements
- Product image order

## Security

### Implemented Measures
- Security headers (XSS, CSRF protection)
- File access restrictions
- xmlrpc.php disabled
- wp-config.php protected

### Recommended Additional Security
- Install security plugin (Wordfence recommended)
- Enable two-factor authentication
- Regular WordPress updates
- SSL certificate installation

## Support & Maintenance

### Regular Tasks
- Update WordPress core and plugins monthly
- Monitor site performance weekly
- Review analytics data monthly
- Backup site weekly

### Troubleshooting
- Check error logs in cPanel or hosting dashboard
- Verify image paths if carousel not working
- Ensure AJAX endpoints are accessible
- Test forms regularly

## Contact Information

For technical support or customization requests:
- Email: <EMAIL>
- Phone: (*************

---

**Built with WordPress and optimized for conversions.**
