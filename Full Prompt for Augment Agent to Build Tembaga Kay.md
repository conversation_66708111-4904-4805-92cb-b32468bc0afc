<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Full Prompt for Augment Agent to Build Tembaga Kayu Copper Fixtures Landing Page

## Project Overview

You are an expert WordPress developer and designer tasked with creating a high-converting, visually compelling landing page for Tembaga Kayu’s hand-forged copper sinks and bathtubs. The page must showcase artisanal craftsmanship, highlight copper’s antimicrobial and patina properties, and drive qualified leads through a “Begin the Dialogue” consultation call-to-action .

## 1. Hero Section

- **Background**: Full-width image of an artisan hand-forging copper with a semi-transparent dark overlay for text clarity .
- **Headline**: “Hand-Forged Copper Artistry Where Time-Honored Techniques Meet Contemporary Design” .
- **Subheadline**: “Each sink and bathtub is shaped layer by layer in dialogue between artisan and element, offering natural antimicrobial protection and a living patina that deepens with time” .
- **Call-to-Action Buttons**:
    - Primary: “Begin the Dialogue” (contrasting copper hue)
    - Secondary: “Explore Our Craft” (text-link style)
- **Responsive Design**: Ensure text scales and buttons remain tappable on screens as small as 375 px width .


## 2. Product Showcase Carousel

- **Images**: Six high-resolution photos (three sinks, three tubs) in a carousel with lightbox zoom on click .
- **Captions**: Include product name, dimensions, finish options, and an anchored “Request Quote” link .
- **Performance**: Lazy-load images for improved page-speed metrics (LCP ≤ 2.5 s) using an optimization plugin .


## 3. Unique Selling Points (USPs) Grid

- **Layout**: Three columns with icon-and-text format .

1. **Antimicrobial Properties**: Copper kills bacteria and viruses within two hours .
2. **Living Patina**: Natural patina evolution that enhances each piece’s character .
3. **Heat Retention**: Superior thermal conductivity for warmer, longer baths .
- **Links**: Each USP has a “Learn More” anchor to a detailed “Why Copper?” section .


## 4. Custom Order Process Infographic

- **Steps**: Four horizontal steps with icons and captions :

1. Consultation – initial discovery call
2. Design Draft – CAD mockup review
3. Hand-Forging – artisan workshop production
4. Delivery – on-site delivery service
- **Downloadable Guide**: “Guide to Commissioning Your Bespoke Copper Fixture” PDF accessible via an email-capture form (max three fields) .


## 5. Photo Management

- **Dynamic Gallery**: Use the native WordPress Media Library or an Elementor Gallery Widget to allow future photo uploads by the client .
- **Admin Instructions**: Document steps to add images, write alt text, captions, and optimize for SEO and performance .


## 6. Technical \& SEO Configuration

- **Theme**: Choose a lightweight, responsive theme (e.g., Astra or GeneratePress) with Gutenberg compatibility .
- **SEO Plugin**: Install and configure Yoast SEO; set focus keyword “hand-forged copper sinks” in meta title, description, H1, and image alt attributes .
- **Schema Markup**: Add schema.org Product markup for each featured item (name, image, description, SKU, aggregateRating if available) .


## 7. Performance Optimization

- **Image Format**: Convert images to WebP (quality 75) and enable server-side caching via WP Rocket or LiteSpeed Cache .
- **Critical CSS \& JS**: Preload critical CSS, defer non-critical JavaScript, and enable GZIP compression .
- **CDN**: Integrate Cloudflare for global asset delivery .


## 8. Conversion \& Analytics

- **CTA Placement**: Include primary CTA after each major section and as a sticky header button on scroll .
- **Tracking**: Embed Google Analytics 4 and Facebook Pixel; set up events for CTA clicks and form submissions .
- **Heatmaps**: Integrate Hotjar on hero and product sections for user behavior insights .


## 9. Content \& Copy Guidelines

- **Tone**: Authoritative yet artisanal; use evocative verbs (“hammer,” “reveal,” “patina”) .
- **Readability**: Keep sentences under 20 words and headings descriptive with keywords near the front .
- **Alt Text**: Describe object and context (e.g., “artisan hammering copper sink edge in workshop”) .

---

References

1. HubSpot: Landing Page Design Principles
2. Unbounce: Best Practices for Hero Images
3. WordStream: Effective Headlines for Conversions
4. Copper Development Association: Antimicrobial Properties
5. Google Developers: Responsive Web Design
6. Elementor: Image Carousel Widget Tutorial
7. WPBeginner: How to Add Captions in WordPress Media
8. GTmetrix: Lazy Loading Images
9. Nielsen Norman Group: Icon Usage in Web Design
10. CDC: Antimicrobial Copper Surfaces
11. Copper Development Association: Patina Information
12. Engineering Toolbox: Thermal Conductivity
13. HubSpot: Anchor Links Best Practices
14. Sprout Social: Infographic Design Tips
15. HubSpot: Lead Magnet Guide Creation
16. WordPress.org: Media Library Guide
17. Yoast: Image SEO Best Practices
18. WPBeginner: Choosing a Fast WordPress Theme
19. Yoast: WordPress SEO Definitive Guide
20. Schema.org: Product Schema Documentation
21. WPBeginner: WebP Conversion Plugins
22. Google Developers: Optimize CSS and JavaScript
23. Cloudflare: Using CDN for Performance
24. Neil Patel: CTA Placement Strategies
25. Google Analytics: Event Tracking Setup
26. Hotjar: Heatmap Implementation Guide
27. Unbounce: Copywriting Best Practices
28. Hemingway App: Readability Guidelines
29. Yoast: Writing Effective Alt Text
