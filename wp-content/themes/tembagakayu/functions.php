<?php
/**
 * Tembaga Kayu Theme Functions
 */

// Theme setup
function tembagakayu_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add custom image sizes for products
    add_image_size('product-carousel', 1000, 500, true);
    add_image_size('product-thumbnail', 300, 200, true);
    add_image_size('hero-background', 1920, 1080, true);
}
add_action('after_setup_theme', 'tembagakayu_setup');

// Enqueue styles and scripts
function tembagakayu_scripts() {
    // Main stylesheet
    wp_enqueue_style('tembagakayu-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Source+Sans+Pro:wght@300;400;600&display=swap', array(), null);
    
    // Custom JavaScript
    wp_enqueue_script('tembagakayu-script', get_template_directory_uri() . '/js/main.js', array(), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('tembagakayu-script', 'ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('tembagakayu_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'tembagakayu_scripts');

// Custom post type for products
function create_product_post_type() {
    register_post_type('copper_product',
        array(
            'labels' => array(
                'name' => 'Copper Products',
                'singular_name' => 'Copper Product',
                'add_new' => 'Add New Product',
                'add_new_item' => 'Add New Copper Product',
                'edit_item' => 'Edit Copper Product',
                'new_item' => 'New Copper Product',
                'view_item' => 'View Copper Product',
                'search_items' => 'Search Copper Products',
                'not_found' => 'No copper products found',
                'not_found_in_trash' => 'No copper products found in Trash'
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
            'menu_icon' => 'dashicons-hammer',
            'rewrite' => array('slug' => 'products'),
        )
    );
}
add_action('init', 'create_product_post_type');

// Add custom meta boxes for products
function add_product_meta_boxes() {
    add_meta_box(
        'product_details',
        'Product Details',
        'product_details_callback',
        'copper_product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_product_meta_boxes');

function product_details_callback($post) {
    wp_nonce_field('save_product_details', 'product_details_nonce');
    
    $dimensions = get_post_meta($post->ID, '_product_dimensions', true);
    $finish = get_post_meta($post->ID, '_product_finish', true);
    $price_range = get_post_meta($post->ID, '_product_price_range', true);
    $features = get_post_meta($post->ID, '_product_features', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="product_dimensions">Dimensions</label></th>';
    echo '<td><input type="text" id="product_dimensions" name="product_dimensions" value="' . esc_attr($dimensions) . '" placeholder="e.g., 24&quot; x 18&quot; x 8&quot;" /></td></tr>';
    
    echo '<tr><th><label for="product_finish">Finish Options</label></th>';
    echo '<td><input type="text" id="product_finish" name="product_finish" value="' . esc_attr($finish) . '" placeholder="e.g., Natural Patina, Hammered Copper" /></td></tr>';
    
    echo '<tr><th><label for="product_price_range">Price Range</label></th>';
    echo '<td><input type="text" id="product_price_range" name="product_price_range" value="' . esc_attr($price_range) . '" placeholder="e.g., $2,500 - $4,000" /></td></tr>';
    
    echo '<tr><th><label for="product_features">Key Features</label></th>';
    echo '<td><textarea id="product_features" name="product_features" rows="4" cols="50" placeholder="List key features, one per line">' . esc_textarea($features) . '</textarea></td></tr>';
    echo '</table>';
}

function save_product_details($post_id) {
    if (!isset($_POST['product_details_nonce']) || !wp_verify_nonce($_POST['product_details_nonce'], 'save_product_details')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    if (isset($_POST['product_dimensions'])) {
        update_post_meta($post_id, '_product_dimensions', sanitize_text_field($_POST['product_dimensions']));
    }
    
    if (isset($_POST['product_finish'])) {
        update_post_meta($post_id, '_product_finish', sanitize_text_field($_POST['product_finish']));
    }
    
    if (isset($_POST['product_price_range'])) {
        update_post_meta($post_id, '_product_price_range', sanitize_text_field($_POST['product_price_range']));
    }
    
    if (isset($_POST['product_features'])) {
        update_post_meta($post_id, '_product_features', sanitize_textarea_field($_POST['product_features']));
    }
}
add_action('save_post', 'save_product_details');

// AJAX handler for consultation form
function handle_consultation_form() {
    check_ajax_referer('tembagakayu_nonce', 'nonce');
    
    $first_name = sanitize_text_field($_POST['first_name']);
    $last_name = sanitize_text_field($_POST['last_name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $project_type = sanitize_text_field($_POST['project_type']);
    $timeline = sanitize_text_field($_POST['timeline']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Send email to admin
    $to = get_option('admin_email');
    $subject = 'New Consultation Request - Tembaga Kayu';
    $body = "New consultation request:\n\n";
    $body .= "Name: {$first_name} {$last_name}\n";
    $body .= "Email: {$email}\n";
    $body .= "Phone: {$phone}\n";
    $body .= "Project Type: {$project_type}\n";
    $body .= "Timeline: {$timeline}\n";
    $body .= "Message: {$message}\n";
    
    $headers = array('Content-Type: text/plain; charset=UTF-8');
    
    if (wp_mail($to, $subject, $body, $headers)) {
        wp_send_json_success('Consultation request sent successfully');
    } else {
        wp_send_json_error('Failed to send consultation request');
    }
}
add_action('wp_ajax_consultation_form', 'handle_consultation_form');
add_action('wp_ajax_nopriv_consultation_form', 'handle_consultation_form');

// AJAX handler for guide download
function handle_guide_download() {
    check_ajax_referer('tembagakayu_nonce', 'nonce');
    
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    
    // Send email with guide
    $to = $email;
    $subject = 'Your Guide to Commissioning Bespoke Copper Fixtures';
    $body = "Dear {$name},\n\n";
    $body .= "Thank you for your interest in our hand-forged copper fixtures!\n\n";
    $body .= "Please find attached your comprehensive guide to commissioning bespoke copper fixtures.\n\n";
    $body .= "If you have any questions, please don't hesitate to contact us.\n\n";
    $body .= "Best regards,\nThe Tembaga Kayu Team";
    
    $headers = array('Content-Type: text/plain; charset=UTF-8');
    
    // In a real implementation, you would attach the PDF guide here
    
    if (wp_mail($to, $subject, $body, $headers)) {
        // Also notify admin of new lead
        $admin_subject = 'New Guide Download - Tembaga Kayu';
        $admin_body = "New guide download:\n\nName: {$name}\nEmail: {$email}\nPhone: {$phone}";
        wp_mail(get_option('admin_email'), $admin_subject, $admin_body, $headers);
        
        wp_send_json_success('Guide sent successfully');
    } else {
        wp_send_json_error('Failed to send guide');
    }
}
add_action('wp_ajax_guide_download', 'handle_guide_download');
add_action('wp_ajax_nopriv_guide_download', 'handle_guide_download');

// Optimize images for performance
function add_webp_support($mimes) {
    $mimes['webp'] = 'image/webp';
    return $mimes;
}
add_filter('upload_mimes', 'add_webp_support');

// Add critical CSS inline
function add_critical_css() {
    echo '<style>
    /* Critical CSS for above-the-fold content */
    body{font-family:Georgia,serif;margin:0;padding:0;background:#faf8f5}
    .hero{height:100vh;min-height:600px;background:linear-gradient(rgba(44,24,16,.6),rgba(44,24,16,.6)),url("' . get_template_directory_uri() . '/../../uploads/2025/01/TK-website-1.jpg");background-size:cover;background-position:center;display:flex;align-items:center;justify-content:center;text-align:center;color:white}
    .hero h1{font-size:4rem;margin-bottom:1.5rem;text-shadow:2px 2px 4px rgba(0,0,0,.5)}
    .btn{display:inline-block;padding:15px 30px;margin:10px;text-decoration:none;border-radius:5px;font-weight:600;font-size:1.1rem;transition:all .3s ease}
    .btn-primary{background-color:#b87333;color:white}
    .btn-secondary{background:transparent;color:white;border:2px solid white}
    </style>';
}
add_action('wp_head', 'add_critical_css', 1);

// Remove unnecessary WordPress features for performance
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'rsd_link');
remove_action('wp_head', 'wp_shortlink_wp_head');

// Disable emoji scripts
remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');

?>
