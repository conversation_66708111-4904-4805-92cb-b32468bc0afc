/*
Theme Name: Tembaga Kayu
Description: Minimalist theme for hand-forged copper fixtures landing page
Version: 2.0
Author: Te<PERSON><PERSON> Kayu
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.7;
    color: #1a1a1a;
    background-color: #ffffff;
    font-weight: 400;
    letter-spacing: -0.01em;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1.5rem;
    color: #1a1a1a;
    letter-spacing: -0.02em;
}

h1 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
}

h2 {
    font-size: 2.25rem;
    font-weight: 600;
}

h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

p {
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.7;
    color: #4a5568;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    min-height: 700px;
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('../../uploads/2025/01/TK-website-1.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.hero-content {
    max-width: 900px;
    padding: 4rem 2rem;
}

.hero h1 {
    color: white;
    font-size: 4.5rem;
    margin-bottom: 2rem;
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.03em;
    animation: fadeInUp 1s ease-out;
}

.hero .subheadline {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
    line-height: 1.6;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    margin: 0.5rem;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    letter-spacing: -0.01em;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: #8b5a3c;
    color: white;
    border: 1px solid #8b5a3c;
}

.btn-primary:hover {
    background-color: #7a4d33;
    border-color: #7a4d33;
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(139, 90, 60, 0.25);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-1px);
}

.hero-buttons {
    animation: fadeInUp 1s ease-out 0.6s both;
    margin-top: 1rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection styling */
::selection {
    background: rgba(139, 90, 60, 0.2);
    color: #1a1a1a;
}

/* Focus styles */
*:focus {
    outline: 2px solid rgba(139, 90, 60, 0.5);
    outline-offset: 2px;
}

/* Additional utility classes */
.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0;
}

.mt-0 {
    margin-top: 0;
}

/* Product Showcase */
.product-showcase {
    padding: 8rem 0;
    background-color: #fafafa;
}

.section-title {
    text-align: center;
    margin-bottom: 5rem;
}

.section-title h2 {
    font-size: 2.5rem;
    color: #1a1a1a;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Carousel */
.carousel-container {
    position: relative;
    max-width: 1100px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.08);
    background: white;
}

.carousel-slide {
    display: none;
    position: relative;
}

.carousel-slide.active {
    display: block;
}

.carousel-slide img {
    width: 100%;
    height: 600px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.carousel-slide img:hover {
    transform: scale(1.02);
}

.carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: white;
    padding: 3rem 2rem 2rem;
    text-align: center;
}

.carousel-caption h3 {
    color: white;
    margin-bottom: 0.75rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.carousel-caption p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
    font-size: 0.95rem;
    line-height: 1.5;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
    border: none;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.25rem;
    color: #1a1a1a;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-nav:hover {
    background: white;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-50%) scale(1.05);
}

.carousel-prev {
    left: 24px;
}

.carousel-next {
    right: 24px;
}

.carousel-dots {
    text-align: center;
    margin-top: 3rem;
}

.carousel-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #d1d5db;
    margin: 0 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.carousel-dot.active {
    background: #8b5a3c;
    transform: scale(1.25);
}

/* USP Grid */
.usp-section {
    padding: 8rem 0;
    background-color: white;
}

.usp-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 4rem;
    margin-top: 5rem;
}

.usp-item {
    text-align: left;
    padding: 3rem 2rem;
    background: #fafafa;
    border-radius: 12px;
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
    position: relative;
}

.usp-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.08);
    border-color: #e5e7eb;
}

.usp-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #8b5a3c;
    border-radius: 12px 0 0 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.usp-item:hover::before {
    opacity: 1;
}

.usp-item h3 {
    margin-bottom: 1.5rem;
    color: #1a1a1a;
    font-size: 1.25rem;
    font-weight: 600;
}

.usp-item p {
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.learn-more {
    color: #8b5a3c;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: color 0.2s ease;
    letter-spacing: -0.01em;
}

.learn-more:hover {
    color: #7a4d33;
}

/* Process Section */
.process-section {
    padding: 8rem 0;
    background-color: #fafafa;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 4rem;
    margin: 5rem 0;
    position: relative;
}

.process-step {
    text-align: left;
    position: relative;
    padding: 2rem 0;
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #8b5a3c;
    color: white;
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
}

.process-step h3 {
    margin-bottom: 1rem;
    color: #1a1a1a;
    font-size: 1.25rem;
    font-weight: 600;
}

.process-step p {
    color: #6b7280;
    line-height: 1.7;
    font-size: 0.95rem;
}

.process-cta {
    text-align: center;
    margin-top: 5rem;
    padding: 4rem 3rem;
    background: white;
    border-radius: 16px;
    border: 1px solid #f3f4f6;
}

.process-cta h3 {
    margin-bottom: 1.5rem;
    color: #1a1a1a;
    font-size: 1.5rem;
    font-weight: 600;
}

.process-cta p {
    color: #6b7280;
    margin-bottom: 2.5rem;
    font-size: 1rem;
}

.lead-magnet {
    margin-top: 2rem;
}

.guide-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: end;
    max-width: 600px;
    margin: 0 auto;
}

.guide-form input {
    flex: 1;
    min-width: 200px;
    padding: 1rem 1.25rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    background: #fafafa;
    transition: all 0.2s ease;
}

.guide-form input:focus {
    outline: none;
    border-color: #8b5a3c;
    box-shadow: 0 0 0 3px rgba(139, 90, 60, 0.1);
    background: white;
}

/* CTA Section */
.cta-section {
    padding: 6rem 0;
    background: #8b5a3c;
    color: white;
    text-align: center;
}

.cta-content h2 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    font-weight: 600;
}

.cta-content p {
    font-size: 1.125rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* Contact Section */
.contact-section {
    padding: 8rem 0;
    background-color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    margin-top: 5rem;
}

.contact-info h3 {
    margin-bottom: 2rem;
    color: #1a1a1a;
    font-size: 1.5rem;
    font-weight: 600;
}

.contact-info p {
    margin-bottom: 3rem;
    color: #6b7280;
    line-height: 1.7;
}

.contact-details {
    margin-bottom: 3rem;
}

.contact-item {
    margin-bottom: 1.5rem;
    color: #6b7280;
    font-size: 0.95rem;
}

.contact-item strong {
    color: #1a1a1a;
    font-weight: 600;
}

.contact-item a {
    color: #8b5a3c;
    text-decoration: none;
    transition: color 0.2s ease;
}

.contact-item a:hover {
    color: #7a4d33;
}

.consultation-form {
    background: #fafafa;
    padding: 3rem;
    border-radius: 16px;
    border: 1px solid #f3f4f6;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.consultation-form input,
.consultation-form select,
.consultation-form textarea {
    padding: 1rem 1.25rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: inherit;
    background: white;
    transition: all 0.2s ease;
}

.consultation-form input:focus,
.consultation-form select:focus,
.consultation-form textarea:focus {
    outline: none;
    border-color: #8b5a3c;
    box-shadow: 0 0 0 3px rgba(139, 90, 60, 0.1);
}

.consultation-form textarea {
    grid-column: 1 / -1;
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

.btn-full {
    width: 100%;
    margin-top: 1.5rem;
    padding: 1.25rem 2rem;
    font-size: 1rem;
}

/* Footer */
.footer {
    background-color: #1a1a1a;
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h4 {
    color: #8b5a3c;
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.2s ease;
    font-size: 0.95rem;
}

.footer-section a:hover {
    color: #8b5a3c;
}

.footer-section p {
    color: #9ca3af;
    font-size: 0.95rem;
    line-height: 1.6;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 2rem;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
}

.footer-bottom a {
    color: #8b5a3c;
    text-decoration: none;
}

/* Sticky CTA */
.sticky-cta {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(26, 26, 26, 0.95);
    color: white;
    padding: 1rem 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(139, 90, 60, 0.3);
}

.sticky-cta .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sticky-cta span {
    font-weight: 500;
    font-size: 0.95rem;
}

.btn-small {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
}

/* Lightbox */
.lightbox {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.95);
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.lightbox-content {
    margin: auto;
    display: block;
    width: 90%;
    max-width: 1200px;
    max-height: 90%;
    object-fit: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 12px;
    box-shadow: 0 25px 100px rgba(0,0,0,0.5);
}

.lightbox-close {
    position: absolute;
    top: 24px;
    right: 32px;
    color: white;
    font-size: 32px;
    font-weight: 400;
    cursor: pointer;
    transition: color 0.2s ease;
    background: rgba(0,0,0,0.5);
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-close:hover {
    color: #8b5a3c;
    background: rgba(0,0,0,0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1.5rem;
    }

    .hero {
        min-height: 600px;
    }

    .hero h1 {
        font-size: 3rem;
    }

    .hero .subheadline {
        font-size: 1.125rem;
    }

    .hero-content {
        padding: 3rem 1.5rem;
    }

    .btn {
        padding: 0.875rem 1.75rem;
        font-size: 0.95rem;
        margin: 0.375rem;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .product-showcase,
    .usp-section,
    .process-section,
    .contact-section {
        padding: 6rem 0;
    }

    .section-title {
        margin-bottom: 4rem;
    }

    .carousel-slide img {
        height: 400px;
    }

    .carousel-nav {
        width: 40px;
        height: 40px;
        font-size: 1.125rem;
    }

    .carousel-prev {
        left: 16px;
    }

    .carousel-next {
        right: 16px;
    }

    .usp-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
        margin-top: 4rem;
    }

    .usp-item {
        padding: 2.5rem 2rem;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 3rem;
        margin: 4rem 0;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 4rem;
        margin-top: 4rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .consultation-form {
        padding: 2.5rem 2rem;
    }

    .guide-form {
        flex-direction: column;
        gap: 1rem;
    }

    .guide-form input {
        min-width: auto;
    }

    .process-cta {
        padding: 3rem 2rem;
        margin-top: 4rem;
    }

    .sticky-cta .container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2.5rem;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2.5rem;
    }

    .hero .subheadline {
        font-size: 1rem;
    }

    .hero-content {
        padding: 2rem 1rem;
    }

    .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
    }

    .carousel-slide img {
        height: 300px;
    }

    .carousel-container {
        border-radius: 12px;
    }

    .consultation-form {
        padding: 2rem 1.5rem;
    }

    .process-cta {
        padding: 2.5rem 1.5rem;
    }

    .usp-item {
        padding: 2rem 1.5rem;
    }

    .section-title h2 {
        font-size: 1.75rem;
    }

    .lightbox-close {
        top: 16px;
        right: 16px;
        width: 40px;
        height: 40px;
        font-size: 24px;
    }
}
