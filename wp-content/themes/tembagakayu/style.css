/*
Theme Name: Tembaga Kayu
Description: Custom theme for hand-forged copper fixtures landing page
Version: 1.0
Author: Tembaga Kayu
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Georgia', serif;
    line-height: 1.6;
    color: #333;
    background-color: #faf8f5;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Georgia', serif;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3.5rem;
    color: #2c1810;
}

h2 {
    font-size: 2.5rem;
    color: #2c1810;
}

h3 {
    font-size: 1.8rem;
    color: #2c1810;
}

p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    background: linear-gradient(rgba(44, 24, 16, 0.6), rgba(44, 24, 16, 0.6)), url('../../uploads/2025/01/TK-website-1.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.hero-content {
    max-width: 800px;
    padding: 2rem;
}

.hero h1 {
    color: white;
    font-size: 4rem;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out;
}

.hero .subheadline {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    color: #f4f1ed;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 15px 30px;
    margin: 10px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
}

.btn-primary {
    background-color: #b87333;
    color: white;
    box-shadow: 0 4px 15px rgba(184, 115, 51, 0.3);
}

.btn-primary:hover {
    background-color: #a0632b;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(184, 115, 51, 0.4);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: #2c1810;
    transform: translateY(-2px);
}

.hero-buttons {
    animation: fadeInUp 1s ease-out 0.6s both;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Product Showcase */
.product-showcase {
    padding: 80px 0;
    background-color: white;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-size: 3rem;
    color: #2c1810;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Carousel */
.carousel-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.carousel-slide {
    display: none;
    position: relative;
}

.carousel-slide.active {
    display: block;
}

.carousel-slide img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    cursor: pointer;
}

.carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 2rem;
    text-align: center;
}

.carousel-caption h3 {
    color: white;
    margin-bottom: 0.5rem;
}

.carousel-caption p {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.8);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    color: #2c1810;
    transition: all 0.3s ease;
}

.carousel-nav:hover {
    background: white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.carousel-prev {
    left: 20px;
}

.carousel-next {
    right: 20px;
}

.carousel-dots {
    text-align: center;
    margin-top: 2rem;
}

.carousel-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    margin: 0 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-dot.active {
    background: #b87333;
}

/* USP Grid */
.usp-section {
    padding: 80px 0;
    background-color: #f8f6f3;
}

.usp-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.usp-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.usp-item:hover {
    transform: translateY(-5px);
}

.usp-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: #b87333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.usp-item h3 {
    margin-bottom: 1rem;
    color: #2c1810;
}

.usp-item p {
    color: #666;
    margin-bottom: 1.5rem;
}

.learn-more {
    color: #b87333;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.learn-more:hover {
    color: #a0632b;
}

/* Process Section */
.process-section {
    padding: 80px 0;
    background-color: white;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin: 3rem 0;
}

.process-step {
    text-align: center;
    position: relative;
    padding: 2rem 1rem;
}

.step-number {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: #b87333;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.step-icon {
    font-size: 3rem;
    margin: 2rem 0 1rem;
}

.process-step h3 {
    margin-bottom: 1rem;
    color: #2c1810;
}

.process-step p {
    color: #666;
    line-height: 1.6;
}

.process-cta {
    text-align: center;
    margin-top: 4rem;
    padding: 3rem;
    background: #f8f6f3;
    border-radius: 10px;
}

.process-cta h3 {
    margin-bottom: 1rem;
    color: #2c1810;
}

.lead-magnet {
    margin-top: 2rem;
}

.guide-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: end;
    max-width: 600px;
    margin: 0 auto;
}

.guide-form input {
    flex: 1;
    min-width: 200px;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.guide-form input:focus {
    outline: none;
    border-color: #b87333;
}

/* CTA Section */
.cta-section {
    padding: 60px 0;
    background: linear-gradient(135deg, #b87333, #a0632b);
    color: white;
    text-align: center;
}

.cta-content h2 {
    color: white;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background-color: #f8f6f3;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-info h3 {
    margin-bottom: 1.5rem;
    color: #2c1810;
}

.contact-info p {
    margin-bottom: 2rem;
    color: #666;
}

.contact-details {
    margin-bottom: 2rem;
}

.contact-item {
    margin-bottom: 1rem;
    color: #666;
}

.contact-item a {
    color: #b87333;
    text-decoration: none;
}

.contact-item a:hover {
    text-decoration: underline;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    color: #b87333;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: #a0632b;
}

.consultation-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.consultation-form input,
.consultation-form select,
.consultation-form textarea {
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    font-family: inherit;
}

.consultation-form input:focus,
.consultation-form select:focus,
.consultation-form textarea:focus {
    outline: none;
    border-color: #b87333;
}

.consultation-form textarea {
    grid-column: 1 / -1;
    resize: vertical;
    min-height: 120px;
}

.btn-full {
    width: 100%;
    margin-top: 1rem;
}

/* Footer */
.footer {
    background-color: #2c1810;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: #b87333;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #b87333;
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 1rem;
    text-align: center;
    color: #999;
}

.footer-bottom a {
    color: #b87333;
    text-decoration: none;
}

/* Sticky CTA */
.sticky-cta {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(44, 24, 16, 0.95);
    color: white;
    padding: 1rem 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border-bottom: 2px solid #b87333;
}

.sticky-cta .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sticky-cta span {
    font-weight: 600;
}

.btn-small {
    padding: 8px 20px;
    font-size: 0.9rem;
}

/* Lightbox */
.lightbox {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    cursor: pointer;
}

.lightbox-content {
    margin: auto;
    display: block;
    width: 90%;
    max-width: 1000px;
    max-height: 90%;
    object-fit: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
}

.lightbox-close:hover {
    color: #b87333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.5rem;
    }

    .hero .subheadline {
        font-size: 1.1rem;
    }

    .btn {
        padding: 12px 24px;
        font-size: 1rem;
        margin: 5px;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .carousel-slide img {
        height: 300px;
    }

    .carousel-nav {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .usp-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .guide-form {
        flex-direction: column;
    }

    .guide-form input {
        min-width: auto;
    }

    .sticky-cta .container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 375px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero .subheadline {
        font-size: 1rem;
    }

    .hero-content {
        padding: 1rem;
    }

    .btn {
        display: block;
        margin: 10px 0;
    }

    .consultation-form {
        padding: 1rem;
    }

    .process-cta {
        padding: 2rem 1rem;
    }
}
