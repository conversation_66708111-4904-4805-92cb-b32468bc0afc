/*
Theme Name: Tembaga Kayu
Description: MACH.la inspired theme for hand-forged copper fixtures
Version: 3.0
Author: Tembaga Kayu
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #000000;
    background-color: #ffffff;
    font-weight: 400;
    letter-spacing: -0.005em;
    font-size: 16px;
}

/* Typography - MACH.la inspired */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.2;
    margin-bottom: 2rem;
    color: #000000;
    letter-spacing: -0.01em;
}

h1 {
    font-size: 4rem;
    font-weight: 300;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

h2 {
    font-size: 2rem;
    font-weight: 400;
    letter-spacing: -0.015em;
}

h3 {
    font-size: 1.25rem;
    font-weight: 400;
    letter-spacing: -0.01em;
}

p {
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.6;
    color: #000000;
    font-weight: 400;
}

/* Container - MACH.la inspired */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 3rem;
}

/* Navigation Header */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.site-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 3rem;
}

.site-logo {
    font-size: 1.5rem;
    font-weight: 400;
    color: #000000;
    text-decoration: none;
    letter-spacing: 0.1em;
}

.nav-links {
    display: flex;
    gap: 3rem;
    list-style: none;
}

.nav-links a {
    color: #000000;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 400;
    transition: opacity 0.2s ease;
}

.nav-links a:hover {
    opacity: 0.6;
}

/* Hero Section - MACH.la inspired */
.hero {
    position: relative;
    height: 100vh;
    min-height: 800px;
    background: url('../../uploads/2025/01/TK-website-1.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: white;
    padding-top: 100px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 3rem;
}

.hero h1 {
    color: white;
    font-size: 5rem;
    margin-bottom: 3rem;
    font-weight: 300;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.hero .subheadline {
    font-size: 1.125rem;
    margin-bottom: 4rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
    line-height: 1.6;
    max-width: 600px;
}

/* Buttons - MACH.la inspired */
.btn {
    display: inline-block;
    padding: 1rem 2.5rem;
    margin: 0.5rem 1rem 0.5rem 0;
    text-decoration: none;
    border: 1px solid #000000;
    font-weight: 400;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    cursor: pointer;
    background: transparent;
    color: #000000;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    font-size: 0.875rem;
}

.btn:hover {
    background-color: #000000;
    color: #ffffff;
}

.btn-primary {
    background-color: #000000;
    color: white;
    border: 1px solid #000000;
}

.btn-primary:hover {
    background-color: transparent;
    color: #000000;
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 1);
    color: #000000;
    border-color: white;
}

.hero-buttons {
    margin-top: 2rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection styling */
::selection {
    background: rgba(139, 90, 60, 0.2);
    color: #1a1a1a;
}

/* Focus styles */
*:focus {
    outline: 2px solid rgba(139, 90, 60, 0.5);
    outline-offset: 2px;
}

/* Additional utility classes */
.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0;
}

.mt-0 {
    margin-top: 0;
}

/* Product Showcase - MACH.la inspired grid */
.product-showcase {
    padding: 10rem 0;
    background-color: #ffffff;
}

.section-title {
    text-align: left;
    margin-bottom: 6rem;
    max-width: 800px;
}

.section-title h2 {
    font-size: 3rem;
    color: #000000;
    margin-bottom: 2rem;
    font-weight: 300;
    letter-spacing: -0.02em;
}

.section-subtitle {
    font-size: 1rem;
    color: #000000;
    max-width: 500px;
    line-height: 1.6;
    font-weight: 400;
}

/* Product Grid - MACH.la inspired */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 8rem;
}

.product-item {
    position: relative;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.product-item:hover {
    opacity: 0.8;
}

.product-item img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    display: block;
}

.product-info {
    padding: 2rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.product-info h3 {
    font-size: 1.25rem;
    font-weight: 400;
    margin-bottom: 0.5rem;
    color: #000000;
}

.product-info p {
    font-size: 0.95rem;
    color: #000000;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.product-link {
    font-size: 0.875rem;
    color: #000000;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #000000;
    padding-bottom: 2px;
    transition: opacity 0.2s ease;
}

.product-link:hover {
    opacity: 0.6;
}

/* Legacy carousel support */
.carousel-container {
    display: none;
}

.carousel-slide {
    display: none;
}

.carousel-nav {
    display: none;
}

.carousel-dots {
    display: none;
}

/* USP Section - MACH.la inspired */
.usp-section {
    padding: 10rem 0;
    background-color: #f8f8f8;
}

.usp-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8rem;
    align-items: start;
}

.usp-intro {
    position: sticky;
    top: 150px;
}

.usp-intro h2 {
    font-size: 2.5rem;
    color: #000000;
    margin-bottom: 2rem;
    font-weight: 300;
    letter-spacing: -0.02em;
}

.usp-intro p {
    font-size: 1rem;
    color: #000000;
    line-height: 1.6;
    opacity: 0.8;
}

.usp-list {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

.usp-item {
    padding: 3rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.usp-item:last-child {
    border-bottom: none;
}

.usp-item h3 {
    font-size: 1.5rem;
    color: #000000;
    margin-bottom: 1.5rem;
    font-weight: 400;
    letter-spacing: -0.01em;
}

.usp-item p {
    color: #000000;
    margin-bottom: 2rem;
    line-height: 1.6;
    opacity: 0.8;
    font-size: 1rem;
}

.learn-more {
    color: #000000;
    text-decoration: none;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #000000;
    padding-bottom: 2px;
    transition: opacity 0.2s ease;
}

.learn-more:hover {
    opacity: 0.6;
}

/* Process Section - MACH.la inspired */
.process-section {
    padding: 10rem 0;
    background-color: #ffffff;
}

.process-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 8rem;
    align-items: start;
}

.process-intro {
    position: sticky;
    top: 150px;
}

.process-intro h2 {
    font-size: 2.5rem;
    color: #000000;
    margin-bottom: 2rem;
    font-weight: 300;
    letter-spacing: -0.02em;
}

.process-intro p {
    font-size: 1rem;
    color: #000000;
    line-height: 1.6;
    opacity: 0.8;
    margin-bottom: 3rem;
}

.process-steps {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

.process-step {
    padding: 3rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.process-step:last-child {
    border-bottom: none;
}

.step-number {
    font-size: 0.875rem;
    color: #000000;
    opacity: 0.6;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.process-step h3 {
    margin-bottom: 1.5rem;
    color: #000000;
    font-size: 1.5rem;
    font-weight: 400;
    letter-spacing: -0.01em;
}

.process-step p {
    color: #000000;
    line-height: 1.6;
    opacity: 0.8;
    font-size: 1rem;
}

.process-cta {
    text-align: center;
    margin-top: 5rem;
    padding: 4rem 3rem;
    background: white;
    border-radius: 16px;
    border: 1px solid #f3f4f6;
}

.process-cta h3 {
    margin-bottom: 1.5rem;
    color: #1a1a1a;
    font-size: 1.5rem;
    font-weight: 600;
}

.process-cta p {
    color: #6b7280;
    margin-bottom: 2.5rem;
    font-size: 1rem;
}

.lead-magnet {
    margin-top: 2rem;
}

.guide-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: end;
    max-width: 600px;
    margin: 0 auto;
}

.guide-form input {
    flex: 1;
    min-width: 200px;
    padding: 1rem 1.25rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    background: #fafafa;
    transition: all 0.2s ease;
}

.guide-form input:focus {
    outline: none;
    border-color: #8b5a3c;
    box-shadow: 0 0 0 3px rgba(139, 90, 60, 0.1);
    background: white;
}

/* CTA Section */
.cta-section {
    padding: 6rem 0;
    background: #8b5a3c;
    color: white;
    text-align: center;
}

.cta-content h2 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    font-weight: 600;
}

.cta-content p {
    font-size: 1.125rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* Contact Section - MACH.la inspired */
.contact-section {
    padding: 10rem 0;
    background-color: #f8f8f8;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8rem;
    margin-top: 5rem;
}

.contact-info h3 {
    margin-bottom: 2rem;
    color: #000000;
    font-size: 2rem;
    font-weight: 300;
    letter-spacing: -0.015em;
}

.contact-info p {
    margin-bottom: 3rem;
    color: #000000;
    line-height: 1.6;
    opacity: 0.8;
}

.contact-details {
    margin-bottom: 3rem;
}

.contact-item {
    margin-bottom: 1.5rem;
    color: #000000;
    font-size: 1rem;
    opacity: 0.8;
}

.contact-item strong {
    color: #000000;
    font-weight: 400;
    opacity: 1;
}

.contact-item a {
    color: #000000;
    text-decoration: none;
    border-bottom: 1px solid #000000;
    transition: opacity 0.2s ease;
}

.contact-item a:hover {
    opacity: 0.6;
}

.consultation-form {
    background: #ffffff;
    padding: 4rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.consultation-form input,
.consultation-form select,
.consultation-form textarea {
    padding: 1.25rem;
    border: 1px solid rgba(0, 0, 0, 0.2);
    font-size: 1rem;
    font-family: inherit;
    background: transparent;
    transition: all 0.2s ease;
}

.consultation-form input:focus,
.consultation-form select:focus,
.consultation-form textarea:focus {
    outline: none;
    border-color: #000000;
}

.consultation-form textarea {
    grid-column: 1 / -1;
    resize: vertical;
    min-height: 150px;
    line-height: 1.6;
}

.btn-full {
    width: 100%;
    margin-top: 2rem;
    padding: 1.25rem 2rem;
    font-size: 0.875rem;
}

/* Footer - MACH.la inspired */
.footer {
    background-color: #000000;
    color: white;
    padding: 6rem 0 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 4rem;
    margin-bottom: 4rem;
}

.footer-section h4 {
    color: #ffffff;
    margin-bottom: 2rem;
    font-size: 1rem;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 1rem;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.2s ease;
    font-size: 0.95rem;
}

.footer-section a:hover {
    color: #ffffff;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
    line-height: 1.6;
}

.footer-brand {
    max-width: 300px;
}

.footer-brand h4 {
    font-size: 1.5rem;
    font-weight: 400;
    letter-spacing: 0.1em;
    margin-bottom: 1.5rem;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.875rem;
}

.footer-bottom a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    margin-left: 2rem;
}

.footer-bottom a:hover {
    color: #ffffff;
}

/* Sticky CTA */
.sticky-cta {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(26, 26, 26, 0.95);
    color: white;
    padding: 1rem 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(139, 90, 60, 0.3);
}

.sticky-cta .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sticky-cta span {
    font-weight: 500;
    font-size: 0.95rem;
}

.btn-small {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
}

/* Lightbox */
.lightbox {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.95);
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.lightbox-content {
    margin: auto;
    display: block;
    width: 90%;
    max-width: 1200px;
    max-height: 90%;
    object-fit: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 12px;
    box-shadow: 0 25px 100px rgba(0,0,0,0.5);
}

.lightbox-close {
    position: absolute;
    top: 24px;
    right: 32px;
    color: white;
    font-size: 32px;
    font-weight: 400;
    cursor: pointer;
    transition: color 0.2s ease;
    background: rgba(0,0,0,0.5);
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-close:hover {
    color: #8b5a3c;
    background: rgba(0,0,0,0.7);
}

/* Responsive Design - MACH.la inspired */
@media (max-width: 1024px) {
    .container {
        padding: 0 2rem;
    }

    .site-nav {
        padding: 1.5rem 2rem;
    }

    .usp-content,
    .process-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 6rem;
    }

    .usp-intro,
    .process-intro {
        position: static;
    }

    .product-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1.5rem;
    }

    .site-nav {
        padding: 1rem 1.5rem;
    }

    .site-logo {
        font-size: 1.25rem;
    }

    .nav-links {
        gap: 2rem;
    }

    .hero {
        min-height: 700px;
        padding-top: 80px;
    }

    .hero h1 {
        font-size: 3.5rem;
    }

    .hero .subheadline {
        font-size: 1rem;
    }

    .hero-content {
        padding: 0 1.5rem;
    }

    .btn {
        padding: 1rem 2rem;
        font-size: 0.875rem;
        margin: 0.25rem 0.5rem 0.25rem 0;
    }

    .section-title h2 {
        font-size: 2.5rem;
    }

    .product-showcase,
    .usp-section,
    .process-section,
    .contact-section {
        padding: 8rem 0;
    }

    .section-title {
        margin-bottom: 5rem;
    }

    .product-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .product-item img {
        height: 400px;
    }

    .usp-content,
    .process-content {
        gap: 5rem;
    }

    .contact-content {
        gap: 5rem;
        margin-top: 4rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .consultation-form {
        padding: 3rem 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-bottom a {
        margin-left: 0;
        margin-right: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .site-nav {
        padding: 1rem;
    }

    .nav-links {
        gap: 1.5rem;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero .subheadline {
        font-size: 0.95rem;
    }

    .hero-content {
        padding: 0 1rem;
    }

    .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
        text-align: center;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .product-showcase,
    .usp-section,
    .process-section,
    .contact-section {
        padding: 6rem 0;
    }

    .product-item img {
        height: 300px;
    }

    .consultation-form {
        padding: 2rem 1.5rem;
    }

    .contact-info h3 {
        font-size: 1.5rem;
    }

    .usp-intro h2,
    .process-intro h2 {
        font-size: 2rem;
    }

    .lightbox-close {
        top: 16px;
        right: 16px;
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
}
