{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/post-author-name", "title": "Author Name", "category": "theme", "description": "The author name.", "textdomain": "default", "attributes": {"textAlign": {"type": "string"}, "isLink": {"type": "boolean", "default": false, "role": "content"}, "linkTarget": {"type": "string", "default": "_self", "role": "content"}}, "usesContext": ["postType", "postId"], "example": {"viewportWidth": 350}, "supports": {"html": false, "spacing": {"margin": true, "padding": true}, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}}, "style": "wp-block-post-author-name"}