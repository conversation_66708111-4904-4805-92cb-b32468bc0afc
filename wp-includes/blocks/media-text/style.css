.wp-block-media-text{
  box-sizing:border-box;
  direction:ltr;
  display:grid;
  grid-template-columns:50% 1fr;
  grid-template-rows:auto;
}
.wp-block-media-text.has-media-on-the-right{
  grid-template-columns:1fr 50%;
}

.wp-block-media-text.is-vertically-aligned-top>.wp-block-media-text__content,.wp-block-media-text.is-vertically-aligned-top>.wp-block-media-text__media{
  align-self:start;
}

.wp-block-media-text.is-vertically-aligned-center>.wp-block-media-text__content,.wp-block-media-text.is-vertically-aligned-center>.wp-block-media-text__media,.wp-block-media-text>.wp-block-media-text__content,.wp-block-media-text>.wp-block-media-text__media{
  align-self:center;
}

.wp-block-media-text.is-vertically-aligned-bottom>.wp-block-media-text__content,.wp-block-media-text.is-vertically-aligned-bottom>.wp-block-media-text__media{
  align-self:end;
}

.wp-block-media-text>.wp-block-media-text__media{
  grid-column:1;
  grid-row:1;
  margin:0;
}

.wp-block-media-text>.wp-block-media-text__content{
  direction:ltr;
  grid-column:2;
  grid-row:1;
  padding:0 8%;
  word-break:break-word;
}

.wp-block-media-text.has-media-on-the-right>.wp-block-media-text__media{
  grid-column:2;
  grid-row:1;
}

.wp-block-media-text.has-media-on-the-right>.wp-block-media-text__content{
  grid-column:1;
  grid-row:1;
}

.wp-block-media-text__media a{
  display:block;
}

.wp-block-media-text__media img,.wp-block-media-text__media video{
  height:auto;
  max-width:unset;
  vertical-align:middle;
  width:100%;
}
.wp-block-media-text.is-image-fill>.wp-block-media-text__media{
  background-size:cover;
  height:100%;
  min-height:250px;
}

.wp-block-media-text.is-image-fill>.wp-block-media-text__media>a{
  display:block;
  height:100%;
}

.wp-block-media-text.is-image-fill>.wp-block-media-text__media img{
  height:1px;
  margin:-1px;
  overflow:hidden;
  padding:0;
  position:absolute;
  width:1px;
  clip:rect(0, 0, 0, 0);
  border:0;
}
.wp-block-media-text.is-image-fill-element>.wp-block-media-text__media{
  height:100%;
  min-height:250px;
  position:relative;
}

.wp-block-media-text.is-image-fill-element>.wp-block-media-text__media>a{
  display:block;
  height:100%;
}

.wp-block-media-text.is-image-fill-element>.wp-block-media-text__media img{
  height:100%;
  object-fit:cover;
  position:absolute;
  width:100%;
}
@media (max-width:600px){
  .wp-block-media-text.is-stacked-on-mobile{
    grid-template-columns:100% !important;
  }
  .wp-block-media-text.is-stacked-on-mobile>.wp-block-media-text__media{
    grid-column:1;
    grid-row:1;
  }
  .wp-block-media-text.is-stacked-on-mobile>.wp-block-media-text__content{
    grid-column:1;
    grid-row:2;
  }
}